diff --git a/email_login_otp.install b/email_login_otp.install
index 6fac98c..e055276 100644
--- a/email_login_otp.install
+++ b/email_login_otp.install
@@ -95,3 +95,20 @@ function email_login_otp_update_10001() {
   ]);
   $schema->addPrimaryKey('otp_settings', ['uid']);
 }
+
+/**
+ * Critical security update: Fix flood control bypass vulnerability.
+ *
+ * This update addresses a critical vulnerability where the AJAX login callback
+ * could bypass authentication when flood control limits were reached, allowing
+ * unauthorized access to accounts without OTP enabled.
+ */
+function email_login_otp_update_10002() {
+  // Clear all caches to ensure the updated AJAX callback is loaded.
+  drupal_flush_all_caches();
+
+  // Log the security update for audit purposes.
+  \Drupal::logger('email_login_otp')->notice('Critical security update applied: Fixed flood control bypass vulnerability in AJAX login callback.');
+
+  return t('Critical security update applied: Fixed flood control bypass vulnerability that could allow unauthorized login when flood limits were reached.');
+}
diff --git a/email_login_otp.module b/email_login_otp.module
index e3ddd60..c8b49de 100644
--- a/email_login_otp.module
+++ b/email_login_otp.module
@@ -62,6 +62,7 @@ function email_login_otp_form_alter(&$form, FormStateInterface $form_state, $for
 function email_login_otp_login_ajax_callback(&$form, FormStateInterface $form_state) {
   $response = new AjaxResponse();
 
+  // Check for form errors first.
   if ($form_state->getErrors()) {
     unset($form['#prefix']);
     unset($form['#suffix']);
@@ -74,11 +75,93 @@ function email_login_otp_login_ajax_callback(&$form, FormStateInterface $form_st
     return $response;
   }
 
-  // Generate OTP against username.
-  // Will save username and otp in tempstore.private
-  // Send OTP in email.
-  // Replace login form with OTP form.
-  $account = user_load_by_name($form_state->getValue('name'));
+  // SECURITY FIX: Validate user credentials before proceeding.
+  // This prevents login bypass when flood control is triggered.
+  $username = $form_state->getValue('name');
+  $password = $form_state->getValue('pass');
+
+  if (empty($username) || empty($password)) {
+    \Drupal::messenger()->addError(t('Username and password are required.'));
+    unset($form['#prefix']);
+    unset($form['#suffix']);
+    $form['status_messages'] = [
+      '#type' => 'status_messages',
+      '#weight' => -10,
+    ];
+    $response->addCommand(new ReplaceCommand('.' . $form['#attributes']['class'][0], $form));
+    return $response;
+  }
+
+  // Check flood control before attempting authentication.
+  $flood = \Drupal::service('user.flood_control');
+  $request = \Drupal::request();
+  $config = \Drupal::config('user.flood');
+
+  // Check if this IP is blocked.
+  if (!$flood->isAllowed('user.failed_login_ip', $config->get('ip_limit'), $config->get('ip_window'))) {
+    \Drupal::messenger()->addError(t('Too many failed login attempts from your IP address. This IP address is temporarily blocked. Try again later.'));
+    unset($form['#prefix']);
+    unset($form['#suffix']);
+    $form['status_messages'] = [
+      '#type' => 'status_messages',
+      '#weight' => -10,
+    ];
+    $response->addCommand(new ReplaceCommand('.' . $form['#attributes']['class'][0], $form));
+    return $response;
+  }
+
+  // Check if this user is blocked.
+  if (!$flood->isAllowed('user.failed_login_user', $config->get('user_limit'), $config->get('user_window'), $username)) {
+    \Drupal::messenger()->addError(t('Too many failed login attempts for this account. It is temporarily blocked. Try again later or request a new password.'));
+    unset($form['#prefix']);
+    unset($form['#suffix']);
+    $form['status_messages'] = [
+      '#type' => 'status_messages',
+      '#weight' => -10,
+    ];
+    $response->addCommand(new ReplaceCommand('.' . $form['#attributes']['class'][0], $form));
+    return $response;
+  }
+
+  // Load user and validate credentials.
+  $account = user_load_by_name($username);
+  if (!$account || !$account->isActive()) {
+    \Drupal::messenger()->addError(t('Unrecognized username or password. Have you forgotten your password?'));
+    unset($form['#prefix']);
+    unset($form['#suffix']);
+    $form['status_messages'] = [
+      '#type' => 'status_messages',
+      '#weight' => -10,
+    ];
+    $response->addCommand(new ReplaceCommand('.' . $form['#attributes']['class'][0], $form));
+    return $response;
+  }
+
+  // Authenticate the user credentials.
+  $user_auth = \Drupal::service('user.auth');
+  $authenticated_uid = $user_auth->authenticate($username, $password);
+
+  if (!$authenticated_uid || $authenticated_uid != $account->id()) {
+    // Register failed login attempt for flood control.
+    $flood->register('user.failed_login_ip', $config->get('ip_window'));
+    $flood->register('user.failed_login_user', $config->get('user_window'), $username);
+
+    \Drupal::messenger()->addError(t('Unrecognized username or password. Have you forgotten your password?'));
+    unset($form['#prefix']);
+    unset($form['#suffix']);
+    $form['status_messages'] = [
+      '#type' => 'status_messages',
+      '#weight' => -10,
+    ];
+    $response->addCommand(new ReplaceCommand('.' . $form['#attributes']['class'][0], $form));
+    return $response;
+  }
+
+  // Clear flood control on successful authentication.
+  $flood->clear('user.failed_login_ip');
+  $flood->clear('user.failed_login_user', $username);
+
+  // Now that credentials are validated, check OTP requirements.
   $otp = \Drupal::service('email_login_otp.otp');
 
   if (!$otp->isEnabled($account->id())) {
