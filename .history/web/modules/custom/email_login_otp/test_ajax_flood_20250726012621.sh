#!/bin/bash

# Simple AJAX flood control test for email_login_otp
SITE_URL="http://local.drupalx.com"
USERNAME="admin"
WRONG_PASSWORD="wrongpass"
CORRECT_PASSWORD="test"

echo "=== AJAX Flood Control Test ==="
echo "Testing email_login_otp AJAX callback security fix"
echo ""

# Function to test AJAX login
test_ajax_login() {
    local password=$1
    local attempt=$2

    echo "Attempt $attempt: Testing with password '$password'"

    # Get fresh session
    curl -s -c cookies.txt "$SITE_URL/user/login" > /dev/null

    # Submit AJAX login
    RESPONSE=$(curl -s -b cookies.txt \
        -X POST \
        -H "Content-Type: application/x-www-form-urlencoded" \
        -H "X-Requested-With: XMLHttpRequest" \
        -d "name=$USERNAME&pass=$password&form_id=user_login_form&_triggering_element_name=op&_triggering_element_value=Log+in&ajax_form=1" \
        "$SITE_URL/user/login?ajax_form=1")

    # Check response
    if echo "$RESPONSE" | grep -q '"command":"redirect"\|RedirectCommand'; then
        echo "  ❌ LOGIN SUCCESSFUL - VULNERABILITY!"
        return 0
    elif echo "$RESPONSE" | grep -q "Too many failed login attempts"; then
        echo "  ✅ Blocked by flood control"
        return 1
    elif echo "$RESPONSE" | grep -q "Unrecognized username or password"; then
        echo "  ✅ Authentication failed (normal)"
        return 1
    elif echo "$RESPONSE" | grep -q '"command":"insert".*error'; then
        echo "  ✅ Error message displayed"
        return 1
    else
        echo "  ⚠️  Unknown response"
        echo "     Response snippet: $(echo "$RESPONSE" | grep -o 'Too many\|Unrecognized\|redirect\|error' | head -3 | tr '\n' ' ')"
        return 2
    fi
}

echo "Step 1: Testing with correct password (should work)"
test_ajax_login "$CORRECT_PASSWORD" "1"
FIRST_RESULT=$?

echo ""
echo "Step 2: Making failed attempts to trigger flood control"

# Make several failed attempts
for i in {1..6}; do
    test_ajax_login "$WRONG_PASSWORD" "$i"
    sleep 1
done

echo ""
echo "Step 3: Testing vulnerability - correct password after flood limit"
echo "If vulnerable, this would succeed despite flood control"

test_ajax_login "$CORRECT_PASSWORD" "FINAL"
FINAL_RESULT=$?

echo ""
echo "=== Results ==="

if [ $FIRST_RESULT -eq 0 ]; then
    echo "✅ Normal login works"
else
    echo "⚠️  Normal login failed - check credentials"
fi

if [ $FINAL_RESULT -eq 0 ]; then
    echo "❌ VULNERABILITY DETECTED!"
    echo "   Login succeeded after flood control - security fix not working!"
elif [ $FINAL_RESULT -eq 1 ]; then
    echo "✅ SECURITY FIX WORKING!"
    echo "   Login properly blocked after flood control"
else
    echo "⚠️  Inconclusive result"
fi

# Cleanup
rm -f cookies.txt
