<?php

namespace Drupal\email_login_otp\Routing;

use <PERSON><PERSON>al\Core\Routing\RouteSubscriberBase;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Routing\RouteCollection;

/**
 * Listens to the dynamic route events.
 */
class EmailLoginOtpRouteSubscriber extends RouteSubscriberBase implements EventSubscriberInterface {

  /**
   * {@inheritdoc}
   */
  protected function alterRoutes(RouteCollection $collection) {
    // Redirect the user.login.http route to custom OTP-protected controller.
    if ($route = $collection->get('user.login.http')) {
      $route->setDefault('_controller', '\Drupal\email_login_otp\Controller\OtpLoginController::validateLoginRequest');
    }
  }

  /**
   * {@inheritdoc}
   */
  public static function getSubscribedEvents(): array {
    $events = parent::getSubscribedEvents();
    // Set a lower priority to ensure this runs after other route subscribers.
    $events['routing.route_alter'] = ['onAlterRoutes', -100];
    return $events;
  }

}
