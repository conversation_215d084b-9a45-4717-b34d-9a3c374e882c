services:
  flood_control.flood_unblock_manager:
    class: <PERSON><PERSON><PERSON>\flood_control\FloodUnblockManagerBase
    arguments: ['@database', '@flood', '@config.factory', '@entity_type.manager', '@messenger', '@logger.factory']

  flood_control.flood_whitelist:
    class: <PERSON><PERSON>al\flood_control\FloodWhiteList
    decorates: flood
    decoration_priority: 9
    public: false
    arguments:
      - '@flood_control.flood_whitelist.inner'
      - '@request_stack'
