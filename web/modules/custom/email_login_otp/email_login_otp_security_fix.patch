diff --git a/README.md b/README.md
index 5b7aadc..2621879 100644
--- a/README.md
+++ b/README.md
@@ -1,4 +1,4 @@
-Email Login OTP module for Drupal 8.x & 9.x.
+Email Login OTP module for Drupal 10.x & 11.x.
 This module adds Email based OTP authentication functionality to Drupal.
 
 INSTALLATION INSTRUCTIONS
@@ -8,12 +8,44 @@ INSTALLATION INSTRUCTIONS
 2.  Enable the module:
     a.  <PERSON><PERSON> as site administrator, visit the Extend page, and enable Email Login OTP.
     b.  Run "drush pm-enable email_login_otp" on the command line.
-3.  No configurations needed.
+3.  Configure the module at `/admin/config/email_login_otp/config`
 4.  Done!
 
+FEATURES
+--------
+* Email-based OTP authentication for both web forms and REST API
+* Configurable OTP enforcement (optional vs mandatory)
+* User-specific OTP settings at `/user/{uid}/2fa-settings`
+* Bypass permissions for privileged users
+* Secure REST API login protection
+* AJAX-based login form integration
+
+SECURITY FEATURES
+-----------------
+* **REST API Protection**: Prevents bypass of OTP via `/user/login?_format=json`
+* **Configurable Enforcement**: Admin can make OTP mandatory for all users
+* **Bypass Permissions**: Fine-grained control via `email_login_otp bypass enforced redirect` permission
+* **Token Validation**: Proper OTP token validation for API requests
+
+CONFIGURATION
+-------------
+Visit `/admin/config/email_login_otp/config` to configure:
+- Allow users to enable/disable OTP
+- Force redirect for users without OTP
+- Resend wait time
+- Redirect messages
+
+USAGE
+-----
+
+### Web Form Login
+1. User enters username/password
+2. If OTP is enabled, user receives email with OTP code
+3. User enters OTP code to complete login
+
 NOTES
 -----
-* This module provides OTP authentication to Login form only.
-* This module overrides the default Login form submit callback and registers its' own ajax based callback.
-* Generated OTP is valid til 5 minutes.
-* No configrations needed.
+* Generated OTP is valid for 5 minutes (configurable)
+* OTP codes are 6-digit random numbers
+* Email templates are customizable via Twig templates
+* Module respects Drupal's flood control for login attempts
diff --git a/email_login_otp.info.yml b/email_login_otp.info.yml
index 49a66c1..983acb3 100644
--- a/email_login_otp.info.yml
+++ b/email_login_otp.info.yml
@@ -1,5 +1,5 @@
 name: 'Email Login OTP'
 type: module
 description: 'Provides login via Email OTP.'
-core_version_requirement: ^9.5 || ^10 || ^11
+core_version_requirement: ^10 || ^11
 configure: email_login_otp.email_login_otp_config_form
diff --git a/email_login_otp.install b/email_login_otp.install
index 6fac98c..466f9bd 100644
--- a/email_login_otp.install
+++ b/email_login_otp.install
@@ -95,3 +95,16 @@ function email_login_otp_update_10001() {
   ]);
   $schema->addPrimaryKey('otp_settings', ['uid']);
 }
+
+/**
+ * Security update: Clear caches to register new route subscriber service.
+ *
+ * This update ensures the new OtpLoginController is properly registered
+ * to protect the REST API login endpoint from OTP bypass.
+ */
+function email_login_otp_update_10002() {
+  // Clear all caches to ensure the new route subscriber is registered.
+  drupal_flush_all_caches();
+
+  return t('Security update applied: REST API login endpoint is now protected by OTP validation.');
+}
diff --git a/email_login_otp.permissions.yml b/email_login_otp.permissions.yml
new file mode 100644
index 0000000..ae3bbda
--- /dev/null
+++ b/email_login_otp.permissions.yml
@@ -0,0 +1,4 @@
+email_login_otp bypass enforced redirect:
+  title: 'Bypass enforced email OTP redirect'
+  description: 'Allow users to bypass the enforced email OTP setup redirect when OTP is mandatory.'
+  restrict access: true
diff --git a/email_login_otp.services.yml b/email_login_otp.services.yml
index 6a08209..2ad27d7 100644
--- a/email_login_otp.services.yml
+++ b/email_login_otp.services.yml
@@ -4,6 +4,10 @@ services:
     arguments: ['@current_user', '@tempstore.private', '@email_login_otp.otp', '@current_route_match', '@config.factory', '@messenger', '@entity_type.manager']
     tags:
       - { name: event_subscriber }
+  email_login_otp.route_subscriber:
+    class: Drupal\email_login_otp\Routing\EmailLoginOtpRouteSubscriber
+    tags:
+      - { name: event_subscriber }
   email_login_otp.otp:
     class: Drupal\email_login_otp\Services\Otp
     arguments: ['@database', '@plugin.manager.mail', '@language_manager', '@password', '@tempstore.private', '@config.factory', '@extension.path.resolver', '@renderer']
diff --git a/src/Controller/OtpLoginController.php b/src/Controller/OtpLoginController.php
new file mode 100644
index 0000000..0337ef6
--- /dev/null
+++ b/src/Controller/OtpLoginController.php
@@ -0,0 +1,210 @@
+<?php
+
+namespace Drupal\email_login_otp\Controller;
+
+use Drupal\Core\Access\CsrfTokenGenerator;
+use Drupal\Core\Config\ConfigFactoryInterface;
+use Drupal\Core\DependencyInjection\ContainerInjectionInterface;
+use Drupal\Core\Entity\EntityTypeManagerInterface;
+use Drupal\Core\Routing\RouteProviderInterface;
+use Drupal\email_login_otp\Services\Otp;
+use Drupal\user\Controller\UserAuthenticationController;
+use Drupal\user\UserAuthenticationInterface;
+use Drupal\user\UserAuthInterface;
+use Drupal\user\UserFloodControlInterface;
+use Drupal\user\UserStorageInterface;
+use Psr\Log\LoggerInterface;
+use Symfony\Component\DependencyInjection\ContainerInterface;
+use Symfony\Component\HttpFoundation\Request;
+use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
+use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
+use Symfony\Component\Serializer\Serializer;
+
+/**
+ * Controller for handling OTP-protected login requests.
+ */
+class OtpLoginController extends UserAuthenticationController implements ContainerInjectionInterface {
+
+  /**
+   * The config factory.
+   *
+   * @var \Drupal\Core\Config\ConfigFactoryInterface
+   */
+  protected $configFactory;
+
+  /**
+   * The OTP service.
+   *
+   * @var \Drupal\email_login_otp\Services\Otp
+   */
+  protected $otpService;
+
+  /**
+   * The entity type manager.
+   *
+   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
+   */
+  protected $entityTypeManager;
+
+  /**
+   * Constructs an OtpLoginController object.
+   *
+   * @param \Drupal\user\UserFloodControlInterface $user_flood_control
+   *   The user flood control service.
+   * @param \Drupal\user\UserStorageInterface $user_storage
+   *   The user storage.
+   * @param \Drupal\Core\Access\CsrfTokenGenerator $csrf_token
+   *   The CSRF token generator.
+   * @param \Drupal\user\UserAuthenticationInterface|\Drupal\user\UserAuthInterface $user_auth
+   *   The user authentication service.
+   * @param \Drupal\Core\Routing\RouteProviderInterface $route_provider
+   *   The route provider.
+   * @param \Symfony\Component\Serializer\Serializer $serializer
+   *   The serializer service.
+   * @param array $serialization_formats
+   *   The available serialization formats.
+   * @param \Psr\Log\LoggerInterface $logger
+   *   The logger service.
+   * @param \Drupal\Core\Config\ConfigFactoryInterface $config_factory
+   *   The config factory.
+   * @param \Drupal\email_login_otp\Services\Otp $otp_service
+   *   The OTP service.
+   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
+   *   The entity type manager.
+   */
+  public function __construct(UserFloodControlInterface $user_flood_control, UserStorageInterface $user_storage, CsrfTokenGenerator $csrf_token, UserAuthenticationInterface|UserAuthInterface $user_auth, RouteProviderInterface $route_provider, Serializer $serializer, array $serialization_formats, LoggerInterface $logger, ConfigFactoryInterface $config_factory, Otp $otp_service, EntityTypeManagerInterface $entity_type_manager) {
+    parent::__construct($user_flood_control, $user_storage, $csrf_token, $user_auth, $route_provider, $serializer, $serialization_formats, $logger);
+    $this->configFactory = $config_factory;
+    $this->otpService = $otp_service;
+    $this->entityTypeManager = $entity_type_manager;
+  }
+
+  /**
+   * {@inheritdoc}
+   */
+  public static function create(ContainerInterface $container) {
+    if ($container->hasParameter('serializer.formats') && $container->has('serializer')) {
+      $serializer = $container->get('serializer');
+      $formats = $container->getParameter('serializer.formats');
+    }
+    else {
+      $formats = ['json'];
+      $encoders = [new \Symfony\Component\Serializer\Encoder\JsonEncoder()];
+      $serializer = new \Symfony\Component\Serializer\Serializer([], $encoders);
+    }
+
+    return new static(
+      $container->get('user.flood_control'),
+      $container->get('entity_type.manager')->getStorage('user'),
+      $container->get('csrf_token'),
+      $container->get('user.auth'),
+      $container->get('router.route_provider'),
+      $serializer,
+      $formats,
+      $container->get('logger.factory')->get('user'),
+      $container->get('config.factory'),
+      $container->get('email_login_otp.otp'),
+      $container->get('entity_type.manager')
+    );
+  }
+
+  /**
+   * Custom login validation with OTP check.
+   *
+   * @param \Symfony\Component\HttpFoundation\Request $request
+   *   The request object.
+   *
+   * @return \Symfony\Component\HttpFoundation\Response
+   *   The response object.
+   *
+   * @throws \Symfony\Component\HttpKernel\Exception\BadRequestHttpException
+   * @throws \Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException
+   */
+  public function validateLoginRequest(Request $request) {
+    $format = $this->getRequestFormat($request);
+    $content = $request->getContent();
+
+    if (empty($content)) {
+      throw new BadRequestHttpException('Empty request body.');
+    }
+
+    $credentials = $this->serializer->decode($content, $format);
+
+    if (empty($credentials['name'])) {
+      throw new BadRequestHttpException('Missing credentials.name.');
+    }
+
+    if (empty($credentials['pass'])) {
+      throw new BadRequestHttpException('Missing credentials.pass.');
+    }
+
+    // Load user by username.
+    /** @var \Drupal\user\UserInterface[] $users */
+    $users = $this->userStorage->loadByProperties(['name' => $credentials['name']]);
+
+    if (count($users) !== 1) {
+      throw new BadRequestHttpException('Sorry, unrecognized username or password.');
+    }
+
+    $user = reset($users);
+
+    // Check if user is active.
+    if (!$user->isActive()) {
+      throw new AccessDeniedHttpException('The user has not been activated or is blocked.');
+    }
+
+    // Check if user has OTP enabled.
+    $user_has_otp = $this->otpService->isEnabled($user->id());
+
+    // Get module configuration.
+    $config = $this->configFactory->get('email_login_otp.config');
+
+    // If OTP is not enabled for user, check if it's enforced.
+    if (!$user_has_otp) {
+      $allow_enable_disable = $config->get('allow_enable_disable') ?? TRUE;
+      $user_can_bypass = $user->hasPermission('email_login_otp bypass enforced redirect');
+
+      // If OTP is enforced and user can't bypass, deny access.
+      if (!$allow_enable_disable && !$user_can_bypass) {
+        throw new AccessDeniedHttpException('Two-factor authentication via email OTP is required but not configured for this user.');
+      }
+
+      // If user can disable OTP or has bypass permission, allow normal login.
+      return parent::login($request);
+    }
+
+    // If user has OTP enabled, require OTP validation.
+    // For API requests, we need to check if OTP token is provided.
+    if (empty($credentials['otp_token'])) {
+      throw new AccessDeniedHttpException('Email OTP token is required for this user.');
+    }
+
+    // Validate the OTP token.
+    $otp_valid = $this->otpService->check($user->id(), $credentials['otp_token']);
+
+    if (!$otp_valid) {
+      throw new AccessDeniedHttpException('Invalid or expired email OTP token.');
+    }
+
+    // If OTP validation passes, proceed with normal login.
+    return parent::login($request);
+  }
+
+  /**
+   * Gets the format of the current request.
+   *
+   * @param \Symfony\Component\HttpFoundation\Request $request
+   *   The current request.
+   *
+   * @return string
+   *   The format of the request.
+   */
+  protected function getRequestFormat(Request $request) {
+    $format = $request->getRequestFormat();
+    if (!in_array($format, $this->serializerFormats)) {
+      throw new BadRequestHttpException("Unrecognized format: $format.");
+    }
+    return $format;
+  }
+
+}
diff --git a/src/EventSubscriber/OtpRedirectSubscriber.php b/src/EventSubscriber/OtpRedirectSubscriber.php
index ef57d2a..190b8ba 100644
--- a/src/EventSubscriber/OtpRedirectSubscriber.php
+++ b/src/EventSubscriber/OtpRedirectSubscriber.php
@@ -136,6 +136,7 @@ class OtpRedirectSubscriber implements EventSubscriberInterface {
       ];
       if (
         !$account->hasRole('administrator') &&
+        !$account->hasPermission('email_login_otp bypass enforced redirect') &&
         !$config->get('allow_enable_disable') &&
         !$this->email_login_otp->isEnabled($this->currentUser->id()) &&
         !in_array($this->routeMatch->getRouteName(), $bypass_routes) &&
diff --git a/src/Routing/EmailLoginOtpRouteSubscriber.php b/src/Routing/EmailLoginOtpRouteSubscriber.php
new file mode 100644
index 0000000..2363edf
--- /dev/null
+++ b/src/Routing/EmailLoginOtpRouteSubscriber.php
@@ -0,0 +1,34 @@
+<?php
+
+namespace Drupal\email_login_otp\Routing;
+
+use Drupal\Core\Routing\RouteSubscriberBase;
+use Symfony\Component\EventDispatcher\EventSubscriberInterface;
+use Symfony\Component\Routing\RouteCollection;
+
+/**
+ * Listens to the dynamic route events.
+ */
+class EmailLoginOtpRouteSubscriber extends RouteSubscriberBase implements EventSubscriberInterface {
+
+  /**
+   * {@inheritdoc}
+   */
+  protected function alterRoutes(RouteCollection $collection) {
+    // Redirect the user.login.http route to custom OTP-protected controller.
+    if ($route = $collection->get('user.login.http')) {
+      $route->setDefault('_controller', '\Drupal\email_login_otp\Controller\OtpLoginController::validateLoginRequest');
+    }
+  }
+
+  /**
+   * {@inheritdoc}
+   */
+  public static function getSubscribedEvents(): array {
+    $events = parent::getSubscribedEvents();
+    // Set a lower priority to ensure this runs after other route subscribers.
+    $events['routing.route_alter'] = ['onAlterRoutes', -100];
+    return $events;
+  }
+
+}
