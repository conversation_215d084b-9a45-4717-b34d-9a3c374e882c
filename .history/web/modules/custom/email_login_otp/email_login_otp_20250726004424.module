<?php

/**
 * @file
 * Contains login_otp.module.
 */

use <PERSON><PERSON>al\Core\Routing\RouteMatchInterface;
use <PERSON><PERSON>al\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\Core\Ajax\AjaxResponse;
use <PERSON><PERSON>al\Core\Ajax\ReplaceCommand;
use <PERSON><PERSON>al\Core\Ajax\RedirectCommand;
use Drupal\Core\Url;

/**
 * Implements hook_help().
 */
function email_login_otp_help($route_name, RouteMatchInterface $route_match) {
  switch ($route_name) {
    // Main module help for the email_login_otp module.
    case 'help.page.email_login_otp':
      $output = '';
      $output .= '<h3>' . t('About') . '</h3>';
      $output .= '<p>' . t('Provides login via Email OTP.') . '</p>';
      return $output;

    default:
  }
}

/**
 * Implements hook_theme().
 */
function email_login_otp_theme($existing, $type, $theme, $path) {
  return [
    'email_login_otp_mail' => [
      'variables' => [
        'username' => '',
        'otp' => '',
      ],
      'template' => 'email_login_otp_mail'
    ]
  ];
}

/**
 * Altering login form to register ajax submit.
 */
function email_login_otp_form_alter(&$form, FormStateInterface $form_state, $form_id) {
  if ($form_id == 'user_login_form') {
    unset($form['#submit'][0]);
    $form['actions']['submit']['#ajax'] = [
      'callback' => 'email_login_otp_login_ajax_callback',
      'event' => 'click',
    ];
  }
}

/**
 * Login form ajax callback.
 */
function email_login_otp_login_ajax_callback(&$form, FormStateInterface $form_state) {
  $response = new AjaxResponse();

  if ($form_state->getErrors()) {
    unset($form['#prefix']);
    unset($form['#suffix']);
    $form['status_messages'] = [
      '#type' => 'status_messages',
      '#weight' => -10,
    ];
    $form_state->setRebuild();
    $response->addCommand(new ReplaceCommand('.' . $form['#attributes']['class'][0], $form));
    return $response;
  }

  // Generate OTP against username.
  // Will save username and otp in tempstore.private
  // Send OTP in email.
  // Replace login form with OTP form.
  $account = user_load_by_name($form_state->getValue('name'));
  $otp = \Drupal::service('email_login_otp.otp');

  if (!$otp->isEnabled($account->id())) {
    user_login_finalize($account);
    $redirect_command = new RedirectCommand(Url::fromRoute('user.page')->toString());
    $response->addCommand($redirect_command);
    return $response;
  }

  $otp_code = $otp->generate($form_state->getValue('name'));
  if ($otp_code && $otp->send($otp_code)) {
    \Drupal::messenger()->addMessage(t('An OTP was sent to you via email. Please check your inbox.'));
    $redirect_command = new RedirectCommand(Url::fromRoute('email_login_otp.otp_form')->toString());
    $response->addCommand($redirect_command);
    return $response;
  }

  unset($form['#prefix']);
  unset($form['#suffix']);
  $form['status_messages'] = [
    '#type' => 'status_messages',
    '#weight' => -10,
  ];
  \Drupal::messenger()->addError(t('An error occurred while trying to generate OTP.'));
  $response->addCommand(new ReplaceCommand('.' . $form['#attributes']['class'][0], $form));
  return $response;
}

/**
 * Implements hook_mail().
 */
function email_login_otp_mail($key, &$message, $params) {
  $message['headers']['MIME-Version'] = '1.0';
  $message['headers']['Content-Type'] = 'text/html; charset=UTF-8; format=flowed';
  $message['headers']['Content-Transfer-Encoding'] = '8Bit';
  $message['headers']['X-Mailer'] = 'Drupal';
  $message['from'] = \Drupal::config('system.site')->get('mail');
  switch ($key) {
    case 'email_login_otp_mail':
      $message['subject'] = t('OTP Code');
      $message['body'][] = $params['message'];
      break;
  }
}
